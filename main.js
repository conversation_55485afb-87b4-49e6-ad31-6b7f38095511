const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// 全局变量
let mainWindow;
let accounts = [];
let currentProduct = {
    name: '电话卡免费试用',
    activityId: '720410',
    skuId: '************',
    activityWareId: '1088126',
    businessGroup: '7007',
    businessType: '118502',
    score: 1,
    sourceCode: 'zszicon',
    exchangeWareType: 1
};
let isRunning = false;
let timerHandle = null;

function createWindow() {
    // 创建浏览器窗口
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 900,
        minWidth: 1000,
        minHeight: 700,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets/icon.png'),
        title: '京东积分兑换工具 v2.0',
        resizable: true,
        minimizable: true,
        maximizable: true
    });

    // 加载应用的 index.html
    mainWindow.loadFile('index.html');

    // 开发模式下打开开发者工具
    if (process.argv.includes('--dev')) {
        mainWindow.webContents.openDevTools();
    }

    // 当窗口被关闭时触发
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

// 当 Electron 完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(createWindow);

// 当所有窗口都被关闭时退出应用
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC 事件处理
ipcMain.handle('get-product-info', () => {
    return currentProduct;
});

ipcMain.handle('update-product', (event, product) => {
    currentProduct = { ...currentProduct, ...product };
    return { success: true, message: '商品配置已更新' };
});

ipcMain.handle('parse-accounts', (event, accountText) => {
    try {
        const lines = accountText.trim().split('\n');
        const parsedAccounts = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;

            // 支持两种格式：
            // 1. 完整cookie格式：pt_key=xxx; pt_pin=xxx;
            // 2. 简单格式：账号名,pt_pin,pt_key

            if (line.includes('pt_key=') && line.includes('pt_pin=')) {
                // 完整cookie格式
                const ptKeyMatch = line.match(/pt_key=([^;]+)/);
                const ptPinMatch = line.match(/pt_pin=([^;]+)/);

                if (!ptKeyMatch || !ptPinMatch) {
                    return {
                        success: false,
                        message: `第${i + 1}行cookie格式错误，请确保包含pt_key和pt_pin`
                    };
                }

                const ptKey = ptKeyMatch[1].trim();
                const ptPin = ptPinMatch[1].trim();
                const accountName = `账号${i + 1}`;

                const account = {
                    name: accountName,
                    ptPin: ptPin,
                    ptKey: ptKey,
                    cookie: line.trim()
                };
                parsedAccounts.push(account);
            } else {
                // 简单格式：账号名,pt_pin,pt_key
                const parts = line.split(',');
                if (parts.length !== 3) {
                    return {
                        success: false,
                        message: `第${i + 1}行格式错误，支持两种格式：\n1. pt_key=xxx; pt_pin=xxx;\n2. 账号名,pt_pin,pt_key`
                    };
                }

                const account = {
                    name: parts[0].trim(),
                    ptPin: parts[1].trim(),
                    ptKey: parts[2].trim(),
                    cookie: `pt_key=${parts[2].trim()}; pt_pin=${parts[1].trim()};`
                };
                parsedAccounts.push(account);
            }
        }

        accounts = parsedAccounts;
        return {
            success: true,
            message: `成功解析 ${parsedAccounts.length} 个账号`,
            accounts: parsedAccounts
        };
    } catch (error) {
        return { success: false, message: `解析失败: ${error.message}` };
    }
});

ipcMain.handle('start-timer', async (event, { hour, minute, second }) => {
    if (isRunning) {
        return { success: false, message: '定时任务已在运行中' };
    }

    if (accounts.length === 0) {
        return { success: false, message: '请先配置账号信息' };
    }

    try {
        const now = new Date();
        const target = new Date();
        target.setHours(parseInt(hour), parseInt(minute), parseInt(second), 0);

        // 如果目标时间已过，设置为明天
        if (target <= now) {
            target.setDate(target.getDate() + 1);
        }

        const delay = target.getTime() - now.getTime();
        isRunning = true;

        // 设置定时器
        timerHandle = setTimeout(async () => {
            try {
                mainWindow.webContents.send('log-message', '🚀 时间到！开始执行兑换...');
                await executeExchange();
            } catch (error) {
                mainWindow.webContents.send('log-message', `❌ 执行兑换时发生错误: ${error.message}`);
            } finally {
                // 确保状态正确重置
                isRunning = false;
                timerHandle = null;
            }
        }, delay);

        return {
            success: true,
            message: `定时任务已启动，将在 ${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:${second.padStart(2, '0')} 执行`,
            delay: Math.round(delay / 1000)
        };
    } catch (error) {
        // 启动失败时重置状态
        isRunning = false;
        timerHandle = null;
        return { success: false, message: `启动失败: ${error.message}` };
    }
});

ipcMain.handle('stop-timer', () => {
    if (!isRunning) {
        return { success: false, message: '没有运行中的定时任务' };
    }

    try {
        // 清除定时器
        if (timerHandle) {
            clearTimeout(timerHandle);
            timerHandle = null;
        }

        // 重置状态
        isRunning = false;

        return { success: true, message: '定时任务已停止' };
    } catch (error) {
        // 即使出错也要重置状态
        isRunning = false;
        timerHandle = null;
        return { success: false, message: `停止失败: ${error.message}` };
    }
});

ipcMain.handle('test-exchange', async () => {
    if (accounts.length === 0) {
        return { success: false, message: '请先配置账号信息' };
    }

    try {
        await executeExchange();
        return { success: true, message: '测试完成' };
    } catch (error) {
        return { success: false, message: `测试失败: ${error.message}` };
    }
});



// 执行兑换 - 优化的并发控制
async function executeExchange() {
    const results = [];
    const concurrencyLimit = 5; // 并发限制，避免请求过多

    mainWindow.webContents.send('log-message', `🚀 开始处理 ${accounts.length} 个账号，并发数: ${concurrencyLimit}`);

    // 分批处理账号
    for (let i = 0; i < accounts.length; i += concurrencyLimit) {
        const batch = accounts.slice(i, i + concurrencyLimit);
        const batchNumber = Math.floor(i / concurrencyLimit) + 1;
        const totalBatches = Math.ceil(accounts.length / concurrencyLimit);

        mainWindow.webContents.send('log-message', `📦 处理第 ${batchNumber}/${totalBatches} 批 (${batch.length} 个账号)`);

        // 并发执行当前批次
        const batchPromises = batch.map(async (account, batchIndex) => {
            const globalIndex = i + batchIndex;
            mainWindow.webContents.send('log-message', `🔄 [${account.name}] 开始兑换... (${globalIndex + 1}/${accounts.length})`);

            try {
                const result = await exchangeProduct(account, currentProduct);
                results[globalIndex] = result;

                if (result.success) {
                    mainWindow.webContents.send('log-message', `✅ [${account.name}] 兑换成功! (${globalIndex + 1}/${accounts.length})`);
                } else {
                    mainWindow.webContents.send('log-message', `❌ [${account.name}] 兑换失败: ${result.message} (${globalIndex + 1}/${accounts.length})`);
                }

                return result;
            } catch (error) {
                const errorResult = {
                    accountName: account.name,
                    success: false,
                    message: error.message
                };
                results[globalIndex] = errorResult;
                mainWindow.webContents.send('log-message', `❌ [${account.name}] 兑换异常: ${error.message} (${globalIndex + 1}/${accounts.length})`);
                return errorResult;
            }
        });

        // 等待当前批次完成
        await Promise.all(batchPromises);

        // 批次间添加小延迟
        if (i + concurrencyLimit < accounts.length) {
            mainWindow.webContents.send('log-message', `⏳ 批次间延迟 500ms...`);
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    // 显示统计结果
    showResults(results);
}

// 单个账号兑换
async function exchangeProduct(account, product) {
    const url = 'https://api.m.jd.com/api?functionId=SEP_EXCHANGE_SUBMIT';
    const payload = buildExchangePayload(product);

    // 简化的请求信息记录
    // mainWindow.webContents.send('log-message', `🔍 [${account.name}] 请求参数: 活动ID=${product.activityId}, SKU=${product.skuId}`);

    try {
        const response = await axios.post(url, payload, {
            headers: {
                'Host': 'api.m.jd.com',
                'Cookie': account.cookie,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': '*/*',
                'Origin': 'https://m-sep.jd.com',
                'Referer': 'https://m-sep.jd.com/'
            },
            timeout: 30000
        });

        const data = response.data;

        // 简化的响应信息记录（仅在失败时显示详细信息）
        if (data.errorCode !== 1000) {
            mainWindow.webContents.send('log-message', `🔍 [${account.name}] 错误响应: ${JSON.stringify(data)}`);
        }

        if (data.errorCode === 1000 && data.errorMsg === 'SUCCESS') {
            return {
                accountName: account.name,
                success: true,
                message: '兑换成功'
            };
        } else {
            // 提供更详细的错误信息
            let detailedMessage = data.errorMsg || '兑换失败';
            if (data.errorCode) {
                detailedMessage += ` (错误码: ${data.errorCode})`;
            }

            return {
                accountName: account.name,
                success: false,
                message: detailedMessage,
                errorCode: data.errorCode,
                fullResponse: data
            };
        }
    } catch (error) {
        mainWindow.webContents.send('log-message', `🔍 [${account.name}] 网络错误: ${error.message}`);
        return {
            accountName: account.name,
            success: false,
            message: `请求失败: ${error.message}`
        };
    }
}

// 构建兑换请求体 - 按照抓包的成功格式
function buildExchangePayload(product) {
    const uuid = uuidv4();

    // 按照抓包成功的请求格式构建
    const bodyStr = `{
        "businessType":${product.businessType},
        "activityId":${product.activityId},
        "quantity":1,
        "score":${product.score},
        "remark":"",
        "uuid":"${uuid}",
        "addrCode":**********,
        "exchangeWareType":1,
        "wareBusinessId":"${product.skuId}",
        "deliveryMode":1,
        "activityWareId":${product.activityWareId || product.skuId},
        "parentActivityWareId":null,
        "parentActivityId":null,
        "stockType":1,
        "upperBusinessType":null,
        "couponIds":[],
        "couponAmount":0,
        "farePrice":0,
        "jdOrderClient":"m",
        "messageCode":"",
        "orderType":1,
        "userAgentId":2,
        "userName":"付先生",
        "phoneNum":"***********",
        "userAddress":"江苏南通市通州区川姜镇川港镇大地幼儿园百货超市",
        "provinceId":12,
        "cityId":965,
        "countyId":967,
        "townId":38505,
        "addressDetail":"川港镇大地幼儿园百货超市",
        "sourceCode":"${product.sourceCode}"
    }`.replace(/\s+/g, '').replace(/"/g, '%22');

    return `appid=h5-sep&functionId=SEP_EXCHANGE_SUBMIT&body=${bodyStr}&client=m&clientVersion=6.0.0`;
}

// 显示结果统计
function showResults(results) {
    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;
    const successRate = (successCount / results.length * 100).toFixed(1);
    
    mainWindow.webContents.send('log-message', '📊 兑换结果统计');
    mainWindow.webContents.send('log-message', '================');
    mainWindow.webContents.send('log-message', `🎯 总计: ${results.length}个账号`);
    mainWindow.webContents.send('log-message', `✅ 成功: ${successCount}个`);
    mainWindow.webContents.send('log-message', `❌ 失败: ${failCount}个`);
    mainWindow.webContents.send('log-message', `📈 成功率: ${successRate}%`);
}
