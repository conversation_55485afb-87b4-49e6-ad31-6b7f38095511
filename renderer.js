const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// 全局变量
let currentAccounts = [];
let timerInterval = null;
let currentStatus = 'idle'; // idle, timer_running, testing, exchanging

// DOM 元素
const elements = {
    // 账号管理
    accountInput: document.getElementById('accountInput'),
    parseAccountsBtn: document.getElementById('parseAccountsBtn'),
    clearAccountsBtn: document.getElementById('clearAccountsBtn'),
    batchExchangeBtn: document.getElementById('batchExchangeBtn'),
    accountStatus: document.getElementById('accountStatus'),
    
    // 商品配置
    productName: document.getElementById('productName'),
    productActivityId: document.getElementById('productActivityId'),
    productSkuId: document.getElementById('productSkuId'),
    productBusinessGroup: document.getElementById('productBusinessGroup'),
    productBusinessType: document.getElementById('productBusinessType'),
    productScore: document.getElementById('productScore'),
    editProductBtn: document.getElementById('editProductBtn'),
    presetProductBtn: document.getElementById('presetProductBtn'),
    
    // 定时器
    hourInput: document.getElementById('hourInput'),
    minuteInput: document.getElementById('minuteInput'),
    secondInput: document.getElementById('secondInput'),
    startTimerBtn: document.getElementById('startTimerBtn'),
    stopTimerBtn: document.getElementById('stopTimerBtn'),
    testExchangeBtn: document.getElementById('testExchangeBtn'),
    
    // 日志
    logContainer: document.getElementById('logContainer'),
    clearLogsBtn: document.getElementById('clearLogsBtn'),
    
    // 状态栏
    statusText: document.getElementById('statusText'),
    accountCount: document.getElementById('accountCount'),
    
    // 模态框
    productModal: document.getElementById('productModal'),
    presetModal: document.getElementById('presetModal'),
    editProductName: document.getElementById('editProductName'),
    editActivityId: document.getElementById('editActivityId'),
    editSkuId: document.getElementById('editSkuId'),
    editBusinessGroup: document.getElementById('editBusinessGroup'),
    editBusinessType: document.getElementById('editBusinessType'),
    editScore: document.getElementById('editScore'),
    editSourceCode: document.getElementById('editSourceCode'),
    saveProductBtn: document.getElementById('saveProductBtn'),
    cancelProductBtn: document.getElementById('cancelProductBtn'),
    cancelPresetBtn: document.getElementById('cancelPresetBtn')
};

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    initializeEventListeners();
    await loadProductInfo();

    // 初始化按钮状态
    elements.stopTimerBtn.disabled = true;
    elements.batchExchangeBtn.disabled = true; // 初始禁用，需要先解析账号
    elements.testExchangeBtn.disabled = true; // 初始禁用，需要先解析账号
    updateStatus('待机中', 'idle');

    addLog('🚀 京东积分兑换工具已启动');
    addLog('💡 支持多账号批量兑换，请先配置账号信息');
    addLog('📋 优化功能：并发控制、实时进度、自动滚动日志');
});

// 初始化事件监听器
function initializeEventListeners() {
    // 账号管理
    elements.parseAccountsBtn.addEventListener('click', parseAccounts);
    elements.clearAccountsBtn.addEventListener('click', clearAccounts);
    elements.batchExchangeBtn.addEventListener('click', batchExchange);
    
    // 商品配置
    elements.editProductBtn.addEventListener('click', showProductModal);
    elements.presetProductBtn.addEventListener('click', showPresetModal);
    elements.saveProductBtn.addEventListener('click', saveProduct);
    elements.cancelProductBtn.addEventListener('click', hideProductModal);
    elements.cancelPresetBtn.addEventListener('click', hidePresetModal);
    
    // 定时器
    elements.startTimerBtn.addEventListener('click', startTimer);
    elements.stopTimerBtn.addEventListener('click', stopTimer);
    elements.testExchangeBtn.addEventListener('click', testExchange);
    
    // 日志
    elements.clearLogsBtn.addEventListener('click', clearLogs);
    
    // 模态框关闭
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', (e) => {
            const modal = e.target.closest('.modal');
            if (modal) modal.style.display = 'none';
        });
    });
    
    // 预设商品选择
    document.querySelectorAll('.preset-item').forEach(item => {
        item.addEventListener('click', () => selectPreset(item.dataset.preset));
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal')) {
            e.target.style.display = 'none';
        }
    });
}

// 解析账号
async function parseAccounts() {
    const accountText = elements.accountInput.value.trim();
    if (!accountText) {
        showStatus('请输入账号信息', 'error');
        return;
    }
    
    try {
        const result = await ipcRenderer.invoke('parse-accounts', accountText);
        if (result.success) {
            currentAccounts = result.accounts;
            showStatus(result.message, 'success');
            updateAccountCount(currentAccounts.length);
            addLog(`✅ ${result.message}`);

            // 启用批量兑换和立即测试按钮
            elements.batchExchangeBtn.disabled = false;
            elements.testExchangeBtn.disabled = false;
            addLog(`🎯 已准备就绪，可以进行批量兑换或立即测试`);
        } else {
            showStatus(result.message, 'error');
            addLog(`❌ ${result.message}`);
            elements.batchExchangeBtn.disabled = true;
            elements.testExchangeBtn.disabled = true;
        }
    } catch (error) {
        showStatus(`解析失败: ${error.message}`, 'error');
        addLog(`❌ 解析失败: ${error.message}`);
    }
}

// 清空账号
function clearAccounts() {
    elements.accountInput.value = '';
    currentAccounts = [];
    updateAccountCount(0);
    elements.batchExchangeBtn.disabled = true; // 禁用批量兑换按钮
    elements.testExchangeBtn.disabled = true; // 禁用立即测试按钮
    showStatus('已清空账号信息', 'success');
    addLog('🗑️ 已清空账号信息');
}

// 加载商品信息
async function loadProductInfo() {
    try {
        const product = await ipcRenderer.invoke('get-product-info');
        updateProductDisplay(product);
    } catch (error) {
        addLog(`❌ 加载商品信息失败: ${error.message}`);
    }
}

// 更新商品显示
function updateProductDisplay(product) {
    elements.productName.textContent = product.name;
    elements.productActivityId.textContent = product.activityId;
    elements.productSkuId.textContent = product.skuId;
    elements.productBusinessGroup.textContent = product.businessGroup || '7007';
    elements.productBusinessType.textContent = product.businessType || '118502';
    elements.productScore.textContent = product.score;
}

// 显示商品编辑模态框
async function showProductModal() {
    try {
        const product = await ipcRenderer.invoke('get-product-info');
        elements.editProductName.value = product.name;
        elements.editActivityId.value = product.activityId;
        elements.editSkuId.value = product.skuId;
        elements.editBusinessGroup.value = product.businessGroup || '7007';
        elements.editBusinessType.value = product.businessType || '118502';
        elements.editScore.value = product.score;
        elements.editSourceCode.value = product.sourceCode || '';
        elements.productModal.style.display = 'block';
    } catch (error) {
        addLog(`❌ 加载商品信息失败: ${error.message}`);
    }
}

// 隐藏商品编辑模态框
function hideProductModal() {
    elements.productModal.style.display = 'none';
}

// 保存商品
async function saveProduct() {
    const product = {
        name: elements.editProductName.value,
        activityId: elements.editActivityId.value,
        skuId: elements.editSkuId.value,
        activityWareId: elements.editSkuId.value, // 默认与skuId相同
        businessGroup: elements.editBusinessGroup.value || '7007',
        businessType: elements.editBusinessType.value || '118502',
        score: parseInt(elements.editScore.value) || 1,
        sourceCode: elements.editSourceCode.value || '',
        exchangeWareType: 1
    };

    try {
        const result = await ipcRenderer.invoke('update-product', product);
        if (result.success) {
            updateProductDisplay(product);
            hideProductModal();
            addLog(`✅ ${result.message}: ${product.name} (需要${product.score}积分, 业务组:${product.businessGroup})`);
        } else {
            addLog(`❌ 更新失败: ${result.message}`);
        }
    } catch (error) {
        addLog(`❌ 保存失败: ${error.message}`);
    }
}

// 显示预设商品模态框
function showPresetModal() {
    elements.presetModal.style.display = 'block';
}

// 隐藏预设商品模态框
function hidePresetModal() {
    elements.presetModal.style.display = 'none';
}

// 选择预设商品
async function selectPreset(presetType) {
    const presets = {
        oil: {
            name: '胜牌全合成机油小保养套餐',
            activityId: '730501',
            skuId: '100228358274',
            businessGroup: '7007',
            businessType: '118502',
            score: 9,
            sourceCode: '618zhengshizhongpindaotanchuang',
            exchangeWareType: 2
        },
        phone: {
            name: '电话卡免费试用',
            activityId: '720410',
            skuId: '100011533682',
            activityWareId: '1088126',
            businessGroup: '7007',
            businessType: '118502',
            score: 1,
            sourceCode: 'zszicon',
            exchangeWareType: 1
        },
        test: {
            name: '测试商品（1积分）',
            activityId: '730501',
            skuId: '100228358274',
            businessGroup: '7007',
            businessType: '118502',
            score: 1,
            sourceCode: '618zhengshizhongpindaotanchuang',
            exchangeWareType: 2
        }
    };
    
    const product = presets[presetType];
    if (product) {
        try {
            const result = await ipcRenderer.invoke('update-product', product);
            if (result.success) {
                updateProductDisplay(product);
                hidePresetModal();
                addLog(`✅ 已选择预设商品: ${product.name}`);
            }
        } catch (error) {
            addLog(`❌ 选择预设失败: ${error.message}`);
        }
    }
}

// 立即测试兑换
async function testExchange() {
    if (currentAccounts.length === 0) {
        addLog('❌ 请先配置账号信息');
        return;
    }

    updateStatus(`立即测试中 - ${currentAccounts.length}个账号`, 'running');
    addLog(`🧪 开始立即测试 ${currentAccounts.length} 个账号...`);

    // 禁用测试按钮防止重复点击
    elements.testExchangeBtn.disabled = true;

    try {
        const result = await ipcRenderer.invoke('test-exchange');
        if (result.success) {
            addLog(`✅ 立即测试完成`);
            updateStatus('测试完成', 'success');
        } else {
            addLog(`❌ ${result.message}`);
            updateStatus('测试失败', 'error');
        }
    } catch (error) {
        addLog(`❌ 立即测试失败: ${error.message}`);
        updateStatus('测试失败', 'error');
    } finally {
        // 恢复按钮状态
        elements.testExchangeBtn.disabled = false;

        // 3秒后恢复待机状态
        setTimeout(() => {
            if (currentStatus !== 'timer_running') {
                updateStatus('待机中', 'idle');
            }
        }, 3000);
    }
}

// 开始定时
async function startTimer() {
    const hour = elements.hourInput.value.padStart(2, '0');
    const minute = elements.minuteInput.value.padStart(2, '0');
    const second = elements.secondInput.value.padStart(2, '0');

    try {
        const result = await ipcRenderer.invoke('start-timer', { hour, minute, second });
        if (result.success) {
            updateStatus(`定时中 - 目标时间: ${hour}:${minute}:${second}`, 'running');
            addLog(`⏰ ${result.message}`);
            addLog(`⏳ 距离执行还有: ${Math.floor(result.delay / 60)}分${result.delay % 60}秒`);

            // 启动倒计时显示
            startCountdown(result.delay);

            // 禁用开始按钮，启用停止按钮
            elements.startTimerBtn.disabled = true;
            elements.stopTimerBtn.disabled = false;
        } else {
            addLog(`❌ ${result.message}`);
            updateStatus('待机中', 'idle');
        }
    } catch (error) {
        addLog(`❌ 启动定时失败: ${error.message}`);
        updateStatus('待机中', 'idle');
    }
}

// 停止定时
async function stopTimer() {
    try {
        const result = await ipcRenderer.invoke('stop-timer');
        if (result.success) {
            updateStatus('待机中', 'idle');
            addLog(`⏹️ ${result.message}`);
            stopCountdown();

            // 恢复按钮状态
            elements.startTimerBtn.disabled = false;
            elements.stopTimerBtn.disabled = true;
        } else {
            addLog(`❌ ${result.message}`);
        }
    } catch (error) {
        addLog(`❌ 停止定时失败: ${error.message}`);
    }
}

// 批量兑换
async function batchExchange() {
    if (currentAccounts.length === 0) {
        addLog('❌ 请先配置账号信息');
        return;
    }

    updateStatus(`批量兑换中 - ${currentAccounts.length}个账号`, 'running');
    addLog(`🚀 开始批量兑换 ${currentAccounts.length} 个账号...`);

    // 禁用批量兑换按钮防止重复点击
    elements.batchExchangeBtn.disabled = true;
    elements.parseAccountsBtn.disabled = true;

    try {
        const result = await ipcRenderer.invoke('test-exchange');
        if (result.success) {
            addLog(`✅ 批量兑换完成`);
            updateStatus('兑换完成', 'success');
        } else {
            addLog(`❌ ${result.message}`);
            updateStatus('兑换失败', 'error');
        }
    } catch (error) {
        addLog(`❌ 批量兑换失败: ${error.message}`);
        updateStatus('兑换失败', 'error');
    } finally {
        // 恢复按钮状态
        elements.batchExchangeBtn.disabled = false;
        elements.parseAccountsBtn.disabled = false;

        // 3秒后恢复待机状态
        setTimeout(() => {
            if (currentStatus !== 'timer_running') {
                updateStatus('待机中', 'idle');
            }
        }, 3000);
    }
}

// 开始倒计时
function startCountdown(seconds) {
    let remaining = seconds;
    currentStatus = 'timer_running';
    let lastLogTime = 0;

    timerInterval = setInterval(() => {
        remaining--;
        if (remaining <= 0) {
            stopCountdown();
            updateStatus('正在执行兑换...', 'running');
            return;
        }

        const hours = Math.floor(remaining / 3600);
        const minutes = Math.floor((remaining % 3600) / 60);
        const secs = remaining % 60;

        let timeStr = '';
        if (hours > 0) {
            timeStr = `${hours}小时${minutes}分${secs}秒`;
        } else if (minutes > 0) {
            timeStr = `${minutes}分${secs}秒`;
        } else {
            timeStr = `${secs}秒`;
        }

        // 更新状态栏（每秒更新，跳过日志记录）
        updateStatus(`定时中 - 剩余: ${timeStr}`, 'running', true);

        // 减少日志频率：每30秒记录一次，或最后10秒每秒记录
        const now = Date.now();
        if (remaining <= 10 || remaining % 30 === 0 || now - lastLogTime >= 30000) {
            addLog(`⏰ 定时中 - 剩余: ${timeStr}`);
            lastLogTime = now;
        }
    }, 1000);
}

// 停止倒计时
function stopCountdown() {
    if (timerInterval) {
        clearInterval(timerInterval);
        timerInterval = null;
    }
    currentStatus = 'idle';
}

// 清空日志
function clearLogs() {
    elements.logContainer.innerHTML = '';
    addLog('📋 日志已清空');
}

// 添加日志
function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = 'log-entry';

    // 根据消息类型设置样式
    if (message.includes('✅') || message.includes('成功')) {
        logEntry.classList.add('success');
    } else if (message.includes('❌') || message.includes('失败') || message.includes('错误')) {
        logEntry.classList.add('error');
    } else if (message.includes('⚠️') || message.includes('警告')) {
        logEntry.classList.add('warning');
    } else if (message.includes('🧪') || message.includes('⏰') || message.includes('📊')) {
        logEntry.classList.add('info');
    }

    logEntry.textContent = `[${timestamp}] ${message}`;
    elements.logContainer.appendChild(logEntry);

    // 滚动到底部
    elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
}

// 显示状态消息
function showStatus(message, type = 'info') {
    elements.accountStatus.textContent = message;
    elements.accountStatus.className = `status-message ${type}`;

    // 3秒后清除状态
    setTimeout(() => {
        elements.accountStatus.textContent = '';
        elements.accountStatus.className = 'status-message';
    }, 3000);
}

// 更新状态栏
function updateStatus(status, type = 'info', skipLog = false) {
    currentStatus = type;
    elements.statusText.textContent = `状态: ${status}`;

    // 根据状态类型设置颜色
    elements.statusText.className = `status-${type}`;

    // 只在非跳过日志且非定时状态更新时记录状态变化
    if (!skipLog && !status.includes('剩余:')) {
        if (type === 'running') {
            addLog(`🔄 状态变更: ${status}`);
        } else if (type === 'success') {
            addLog(`✅ 状态变更: ${status}`);
        } else if (type === 'error') {
            addLog(`❌ 状态变更: ${status}`);
        }
    }
}

// 更新账号数量
function updateAccountCount(count) {
    elements.accountCount.textContent = `账号数量: ${count}`;
}

// 监听主进程的日志消息
ipcRenderer.on('log-message', (event, message) => {
    addLog(message);

    // 根据日志消息更新状态
    if (message.includes('时间到！开始执行兑换')) {
        updateStatus('正在执行兑换...', 'running');
        // 恢复按钮状态
        elements.startTimerBtn.disabled = false;
        elements.stopTimerBtn.disabled = true;
    } else if (message.includes('兑换结果统计')) {
        updateStatus('兑换完成', 'success');
        // 5秒后恢复待机状态
        setTimeout(() => {
            updateStatus('待机中', 'idle');
        }, 5000);
    }
});

// 键盘快捷键
document.addEventListener('keydown', (e) => {
    // Ctrl+Enter 解析账号
    if (e.ctrlKey && e.key === 'Enter' && document.activeElement === elements.accountInput) {
        e.preventDefault();
        parseAccounts();
    }

    // Escape 关闭模态框
    if (e.key === 'Escape') {
        document.querySelectorAll('.modal').forEach(modal => {
            if (modal.style.display === 'block') {
                modal.style.display = 'none';
            }
        });
    }
});

// 输入验证
elements.hourInput.addEventListener('input', (e) => {
    const value = parseInt(e.target.value);
    if (value < 0) e.target.value = 0;
    if (value > 23) e.target.value = 23;
});

elements.minuteInput.addEventListener('input', (e) => {
    const value = parseInt(e.target.value);
    if (value < 0) e.target.value = 0;
    if (value > 59) e.target.value = 59;
});

elements.secondInput.addEventListener('input', (e) => {
    const value = parseInt(e.target.value);
    if (value < 0) e.target.value = 0;
    if (value > 59) e.target.value = 59;
});

// 自动保存功能
let autoSaveTimer;
elements.accountInput.addEventListener('input', () => {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(() => {
        localStorage.setItem('jd-accounts', elements.accountInput.value);
    }, 1000);
});

// 加载保存的账号信息
window.addEventListener('load', () => {
    const savedAccounts = localStorage.getItem('jd-accounts');
    if (savedAccounts) {
        elements.accountInput.value = savedAccounts;
    }
});

// 应用关闭前保存数据
window.addEventListener('beforeunload', () => {
    localStorage.setItem('jd-accounts', elements.accountInput.value);
});

// 工具函数：格式化时间
function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
        return `${hours}小时${minutes}分${secs}秒`;
    } else if (minutes > 0) {
        return `${minutes}分${secs}秒`;
    } else {
        return `${secs}秒`;
    }
}

// 工具函数：复制到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        return false;
    }
}

// 添加右键菜单功能
elements.logContainer.addEventListener('contextmenu', (e) => {
    e.preventDefault();
    // 这里可以添加右键菜单功能，比如复制日志等
});

// 主题切换功能（可选）
function toggleTheme() {
    document.body.classList.toggle('dark-theme');
    localStorage.setItem('theme', document.body.classList.contains('dark-theme') ? 'dark' : 'light');
}

// 加载主题设置
window.addEventListener('load', () => {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    }
});
