<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>京东积分兑换工具 v2.0</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- 标题栏 -->
        <header class="header">
            <h1>🚀 京东多账号定时积分兑换工具</h1>
            <div class="version">v2.0 - Electron版</div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧配置区域 -->
            <div class="left-panel">
                <!-- 账号管理区域 -->
                <div class="section">
                    <h3>📝 账号配置</h3>
                    <p class="help-text">每行一个账号：pt_key=xxx; pt_pin=xxx;</p>
                    <textarea
                        id="accountInput"
                        placeholder="pt_key=app_openAAJoT9qiADDTM3nEAQO2ut7cZVt8vs-YgUDeNDzJjKjE5NKfGO8eS2dCfTKjHPY8Ko0YYN3TgAM; pt_pin=jd_6b271cba421da;"
                        rows="10"
                    ></textarea>
                    <div class="button-group">
                        <button id="parseAccountsBtn" class="btn btn-primary">📥 解析账号</button>
                        <button id="clearAccountsBtn" class="btn btn-secondary">🗑️ 清空</button>
                        <button id="batchExchangeBtn" class="btn btn-success">🚀 批量兑换</button>
                    </div>
                    <div id="accountStatus" class="status-message"></div>
                </div>

                <!-- 商品配置区域 -->
                <div class="section">
                    <h3>🛍️ 当前商品信息</h3>
                    <div class="product-info">
                        <div class="info-item">
                            <label>商品名称:</label>
                            <span id="productName">胜牌全合成机油小保养套餐</span>
                        </div>
                        <div class="info-item">
                            <label>活动ID:</label>
                            <span id="productActivityId">730501</span>
                        </div>
                        <div class="info-item">
                            <label>商品SKU:</label>
                            <span id="productSkuId">************</span>
                        </div>
                        <div class="info-item">
                            <label>业务组:</label>
                            <span id="productBusinessGroup">7007</span>
                        </div>
                        <div class="info-item">
                            <label>业务类型:</label>
                            <span id="productBusinessType">118502</span>
                        </div>
                        <div class="info-item">
                            <label>所需积分:</label>
                            <span id="productScore">9</span>
                        </div>
                    </div>
                    <div class="button-group">
                        <button id="editProductBtn" class="btn btn-primary">✏️ 编辑商品</button>
                        <button id="presetProductBtn" class="btn btn-secondary">📋 预设商品</button>
                    </div>
                </div>

                <!-- 定时兑换区域 -->
                <div class="section">
                    <h3>⏰ 定时设置</h3>
                    <div class="time-input">
                        <label>执行时间:</label>
                        <input type="number" id="hourInput" min="0" max="23" value="9" class="time-field">
                        <span>时</span>
                        <input type="number" id="minuteInput" min="0" max="59" value="59" class="time-field">
                        <span>分</span>
                        <input type="number" id="secondInput" min="0" max="59" value="59" class="time-field">
                        <span>秒</span>
                    </div>
                    <div class="button-group">
                        <button id="startTimerBtn" class="btn btn-success">▶️ 开始定时</button>
                        <button id="stopTimerBtn" class="btn btn-danger">⏹️ 停止定时</button>
                        <button id="testExchangeBtn" class="btn btn-warning">🧪 立即测试</button>
                    </div>
                </div>
            </div>

            <!-- 右侧日志区域 -->
            <div class="right-panel">
                <div class="section log-section">
                    <div class="log-header">
                        <h3>📋 运行日志</h3>
                        <button id="clearLogsBtn" class="btn btn-secondary">🗑️ 清空日志</button>
                    </div>
                    <div id="logContainer" class="log-container"></div>
                </div>
            </div>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <div id="statusText">状态: 待机中</div>
            <div id="accountCount">账号数量: 0</div>
        </footer>
    </div>

    <!-- 商品编辑模态框 -->
    <div id="productModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑商品信息</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="editProductName">商品名称:</label>
                    <input type="text" id="editProductName" class="form-input">
                </div>
                <div class="form-group">
                    <label for="editActivityId">活动ID:</label>
                    <input type="text" id="editActivityId" class="form-input">
                </div>
                <div class="form-group">
                    <label for="editSkuId">商品SKU:</label>
                    <input type="text" id="editSkuId" class="form-input">
                </div>
                <div class="form-group">
                    <label for="editBusinessGroup">业务组:</label>
                    <input type="text" id="editBusinessGroup" class="form-input" placeholder="例如: 7007">
                </div>
                <div class="form-group">
                    <label for="editBusinessType">业务类型:</label>
                    <input type="text" id="editBusinessType" class="form-input" placeholder="例如: 118502">
                </div>
                <div class="form-group">
                    <label for="editScore">所需积分:</label>
                    <input type="number" id="editScore" class="form-input">
                </div>
                <div class="form-group">
                    <label for="editSourceCode">来源代码:</label>
                    <input type="text" id="editSourceCode" class="form-input">
                </div>
            </div>
            <div class="modal-footer">
                <button id="saveProductBtn" class="btn btn-primary">💾 保存</button>
                <button id="cancelProductBtn" class="btn btn-secondary">❌ 取消</button>
            </div>
        </div>
    </div>

    <!-- 预设商品模态框 -->
    <div id="presetModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>选择预设商品</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="preset-list">
                    <div class="preset-item" data-preset="oil">
                        <h4>胜牌全合成机油小保养套餐</h4>
                        <p>需要积分: 9分 | 活动ID: 730501 | 业务组: 7007</p>
                    </div>
                    <div class="preset-item" data-preset="phone">
                        <h4>电话卡免费试用</h4>
                        <p>需要积分: 1分 | 活动ID: 720410 | 业务组: 7007</p>
                    </div>
                    <div class="preset-item" data-preset="test">
                        <h4>测试商品（1积分）</h4>
                        <p>需要积分: 1分 | 用于测试兑换功能</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancelPresetBtn" class="btn btn-secondary">❌ 取消</button>
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
