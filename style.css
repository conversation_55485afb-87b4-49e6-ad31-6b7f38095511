/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow: hidden;
}

.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 标题栏 */
.header {
    background: rgba(255, 255, 255, 0.95);
    padding: 15px 20px;
    border-bottom: 2px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header h1 {
    color: #2c3e50;
    font-size: 24px;
    font-weight: bold;
}

.version {
    background: #3498db;
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    margin: 10px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    gap: 15px;
    padding: 20px;
}

/* 左侧面板 */
.left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow-y: auto;
    max-height: calc(100vh - 150px);
    padding-right: 10px;
}

/* 右侧面板 */
.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 400px;
}

/* 日志区域特殊样式 */
.log-section {
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 150px);
}

.log-section .log-container {
    flex: 1;
    min-height: 600px;
    max-height: calc(100vh - 200px);
}

/* 区块标题图标样式 */
.section h3 {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 优化按钮组布局 */
.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.button-group .btn {
    margin: 0;
}

/* 区块样式 */
.section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
}

.section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 18px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

/* 表单元素 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #495057;
}

.form-input, textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-input:focus, textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

textarea {
    resize: vertical;
    min-height: 120px;
    font-family: 'Consolas', 'Monaco', monospace;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    margin-right: 10px;
    margin-bottom: 10px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    font-weight: bold;
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-1px);
}

.btn-success:disabled {
    background: linear-gradient(135deg, #95a5a6, #bdc3c7);
    cursor: not-allowed;
    transform: none;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.button-group {
    margin-top: 15px;
}

/* 帮助文本 */
.help-text {
    color: #6c757d;
    font-size: 13px;
    margin-bottom: 10px;
    font-style: italic;
}

/* 状态消息 */
.status-message {
    margin-top: 10px;
    padding: 10px;
    border-radius: 5px;
    font-weight: bold;
}

.status-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 商品信息显示 */
.product-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.info-item {
    display: flex;
    margin-bottom: 8px;
}

.info-item label {
    min-width: 100px;
    font-weight: bold;
    color: #495057;
}

.info-item span {
    color: #2c3e50;
}

/* 时间输入 */
.time-input {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.time-field {
    width: 60px;
    padding: 8px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    text-align: center;
    font-size: 14px;
}

.time-field:focus {
    outline: none;
    border-color: #3498db;
}

/* 帮助区域 */
.help-section {
    background: #e8f4fd;
    padding: 15px;
    border-radius: 5px;
    margin-top: 20px;
    border-left: 4px solid #3498db;
}

.help-section h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.help-section ul {
    margin-left: 20px;
}

.help-section li {
    margin-bottom: 5px;
    color: #495057;
}

/* 日志区域 */
.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.log-container {
    background: #1e1e1e;
    color: #f8f8f2;
    padding: 15px;
    border-radius: 5px;
    height: 500px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 13px;
    line-height: 1.4;
    scroll-behavior: smooth;
    border: 1px solid #444;
}

.log-container::-webkit-scrollbar {
    width: 8px;
}

.log-container::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: #777;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
}

.log-entry.success {
    color: #50fa7b;
}

.log-entry.error {
    color: #ff5555;
}

.log-entry.warning {
    color: #f1fa8c;
}

.log-entry.info {
    color: #8be9fd;
}

/* 状态栏 */
.status-bar {
    background: rgba(255, 255, 255, 0.95);
    padding: 10px 20px;
    border-top: 2px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
}

#statusText {
    color: #2c3e50;
    font-weight: bold;
    transition: color 0.3s ease;
}

#statusText.status-idle {
    color: #2c3e50;
}

#statusText.status-running {
    color: #f39c12;
    animation: pulse 2s infinite;
}

#statusText.status-success {
    color: #27ae60;
}

#statusText.status-error {
    color: #e74c3c;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

#accountCount {
    color: #3498db;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: #3498db;
    color: white;
    padding: 15px 20px;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.close {
    color: white;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ecf0f1;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 预设商品列表 */
.preset-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.preset-item {
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preset-item:hover {
    border-color: #3498db;
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.preset-item.selected {
    border-color: #3498db;
    background: #e8f4fd;
}

.preset-item h4 {
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 16px;
}

.preset-item p {
    color: #6c757d;
    font-size: 14px;
    margin: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header h1 {
        font-size: 20px;
    }

    .main-content {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }

    .left-panel, .right-panel {
        flex: none;
    }

    .log-section .log-container {
        min-height: 400px;
        height: 400px;
    }

    .section {
        padding: 15px;
    }

    .time-input {
        flex-wrap: wrap;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
